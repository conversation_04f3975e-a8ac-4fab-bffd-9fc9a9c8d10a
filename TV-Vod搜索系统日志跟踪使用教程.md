# TV-Vod 搜索系统日志跟踪使用教程

## 🎯 概述

本教程详细介绍如何使用TV-Vod项目中新增的搜索系统日志跟踪功能，包括日志查看、分析和问题排查方法。

## 📋 日志系统功能特性

### 🔍 完整的搜索链路跟踪
- **FlowID机制**：每次搜索都有唯一标识符
- **多阶段日志**：从搜索初始化到结果选择的完整记录
- **多站点并发**：并发搜索的详细跟踪
- **性能监控**：搜索耗时和成功率统计

### 📊 日志分类体系

| 日志类型 | 标识符 | 描述 | 重要程度 |
|----------|--------|------|----------|
| SEARCH_INIT | 搜索初始化 | 搜索开始的基本信息 | 🟢 INFO |
| MULTI_SITE_SEARCH_START | 多站点搜索开始 | 并发搜索启动 | 🟢 INFO |
| SINGLE_SITE_SEARCH | 单站点搜索 | 具体站点搜索执行 | 🟢 INFO |
| SITE_INFO | 站点信息 | 站点配置和能力 | 🟢 INFO |
| SEARCH_CONTENT_START | 搜索内容开始 | SiteViewModel搜索开始 | 🟢 INFO |
| SEARCH_JS_SPIDER | JS Spider搜索 | JavaScript爬虫搜索 | 🟢 INFO |
| SEARCH_HTTP_API | HTTP API搜索 | HTTP接口搜索 | 🟢 INFO |
| SPIDER_SEARCH_START | Spider搜索开始 | 爬虫层搜索开始 | 🟢 INFO |
| SPIDER_SEARCH_SUCCESS | Spider搜索成功 | 爬虫搜索成功 | 🟢 INFO |
| SPIDER_SEARCH_PARSE | Spider结果解析 | 搜索结果解析 | 🟢 INFO |
| SEARCH_RESULT_RECEIVED | 搜索结果接收 | 接收到搜索结果 | 🟢 INFO |
| SEARCH_RESULT_FILTER | 搜索结果过滤 | 结果过滤处理 | 🟢 INFO |
| SEARCH_RESULT_ADD | 搜索结果添加 | 添加到适配器 | 🟢 INFO |
| SEARCH_RESULT_DETAIL | 搜索结果详情 | 具体结果信息 | 🟢 INFO |
| AUTO_SELECT_RESULT | 自动选择结果 | 自动选择最佳结果 | 🟢 INFO |
| SEARCH_ERROR | 搜索错误 | 搜索过程中的错误 | 🔴 ERROR |
| SPIDER_SEARCH_ERROR | Spider搜索错误 | 爬虫搜索错误 | 🔴 ERROR |

## 🛠️ 使用方法

### 1. 启用日志跟踪

确保在应用中启用了日志输出：

```bash
# 使用ADB查看实时日志
adb logcat | grep "VOD_FLOW"

# 过滤特定FlowID的日志
adb logcat | grep "FlowID:MOVIE_12345"

# 只查看搜索相关日志
adb logcat | grep "SEARCH"
```

### 2. 触发搜索流程

#### 方法1：通过msearch协议
1. 在首页点击推荐电影（ID为msearch:home）
2. 系统自动启动搜索模式
3. 观察日志输出

#### 方法2：手动搜索
1. 在搜索界面输入关键词
2. 点击搜索按钮
3. 观察多站点搜索过程

### 3. 日志分析示例

#### 完整搜索流程日志示例：

```
=== 搜索初始化 ===
[12:34:56.123] [FlowID:MOVIE_1704067200123] [SEARCH_INIT] 初始化搜索: 关键词=朝雪录，自动模式=true

=== 多站点搜索启动 ===
[12:34:56.125] [FlowID:MOVIE_1704067200123] [MULTI_SITE_SEARCH_START] 开始多站点搜索: 关键词=朝雪录，站点数=8

=== 单站点搜索执行 ===
[12:34:56.127] [FlowID:MOVIE_1704067200123] [SINGLE_SITE_SEARCH] 执行单站点搜索: 关键词=朝雪录，站点=影视站点(site1), 类型=3, 支持快速搜索=true
[12:34:56.128] [FlowID:MOVIE_1704067200123] [SITE_INFO] 站点详情: API=http://api.site1.com, 可搜索=true, 可切换=true

=== SiteViewModel搜索处理 ===
[12:34:56.130] [FlowID:MOVIE_1704067200123] [SEARCH_CONTENT_START] 开始搜索内容: 站点=影视站点(site1), 关键词=朝雪录→朝雪录, 快速搜索=true
[12:34:56.132] [FlowID:MOVIE_1704067200123] [SEARCH_JS_SPIDER] JavaScript Spider搜索: 站点=site1

=== Spider层搜索执行 ===
[12:34:56.135] [FlowID:MOVIE_1704067200123] [SPIDER_SEARCH_START] Spider搜索开始 [site1] 关键词:朝雪录, 快速搜索:true
[12:34:56.892] [FlowID:MOVIE_1704067200123] [SPIDER_SEARCH_SUCCESS] Spider搜索成功 [site1] 耗时:757ms, 数据长度:2048
[12:34:56.895] [FlowID:MOVIE_1704067200123] [SPIDER_SEARCH_PARSE] Spider搜索解析 [site1] JSON结果数量:3

=== 搜索结果处理 ===
[12:34:56.898] [FlowID:MOVIE_1704067200123] [SEARCH_JS_RESPONSE] JavaScript Spider响应: 站点=site1, 耗时=768ms, 数据长度=2048
[12:34:56.900] [FlowID:MOVIE_1704067200123] [SEARCH_RESULT_PARSE] 解析搜索结果: 站点=site1, 结果数=3
[12:34:56.902] [FlowID:MOVIE_1704067200123] [SEARCH_CONTENT_COMPLETE] 搜索完成: 站点=site1, 总耗时=772ms

=== 结果接收和过滤 ===
[12:34:56.905] [FlowID:MOVIE_1704067200123] [SEARCH_RESULT_RECEIVED] 接收搜索结果: 站点=影视站点(site1), 原始结果数=3
[12:34:56.907] [FlowID:MOVIE_1704067200123] [SEARCH_RESULT_FILTER] 结果过滤: 站点=site1, 原始=3, 过滤掉=0, 最终=3
[12:34:56.909] [FlowID:MOVIE_1704067200123] [SEARCH_RESULT_ADD] 添加到适配器: 站点=site1, 数量=3, 当前总数=3

=== 搜索结果详情 ===
[12:34:56.911] [FlowID:MOVIE_1704067200123] [SEARCH_RESULT_DETAIL] 结果详情[1]: ID=12345, 名称=朝雪录, 年份=2024
[12:34:56.912] [FlowID:MOVIE_1704067200123] [SEARCH_RESULT_DETAIL] 结果详情[2]: ID=12346, 名称=朝雪录 第二季, 年份=2024
[12:34:56.913] [FlowID:MOVIE_1704067200123] [SEARCH_RESULT_DETAIL] 结果详情[3]: ID=12347, 名称=朝雪录 电影版, 年份=2024

=== 自动选择结果 ===
[12:34:56.920] [FlowID:MOVIE_1704067200123] [AUTO_SELECT_RESULT] 自动选择搜索结果: 电影=朝雪录，真实ID=12345，站点=site1
```

## 🔧 问题排查指南

### 1. 搜索无结果问题

**症状：** 搜索后没有找到任何结果

**排查步骤：**
1. 检查是否有站点参与搜索
   ```bash
   adb logcat | grep "MULTI_SITE_SEARCH_START"
   ```

2. 检查各站点搜索状态
   ```bash
   adb logcat | grep "SINGLE_SITE_SEARCH"
   ```

3. 检查是否有搜索错误
   ```bash
   adb logcat | grep "SEARCH_ERROR"
   ```

4. 检查搜索结果是否被过滤
   ```bash
   adb logcat | grep "SEARCH_RESULT_FILTER"
   ```

### 2. 搜索速度慢问题

**症状：** 搜索响应时间过长

**排查步骤：**
1. 检查各站点搜索耗时
   ```bash
   adb logcat | grep "耗时" | grep "SEARCH"
   ```

2. 识别慢速站点
   ```bash
   adb logcat | grep "SPIDER_SEARCH_SUCCESS" | grep "耗时"
   ```

3. 检查是否有超时站点
   ```bash
   adb logcat | grep "SPIDER_SEARCH_ERROR"
   ```

### 3. 搜索结果不准确问题

**症状：** 搜索结果与关键词不匹配

**排查步骤：**
1. 检查关键词转换
   ```bash
   adb logcat | grep "关键词.*→"
   ```

2. 检查结果过滤逻辑
   ```bash
   adb logcat | grep "SEARCH_RESULT_FILTER"
   ```

3. 查看具体搜索结果
   ```bash
   adb logcat | grep "SEARCH_RESULT_DETAIL"
   ```

## 📈 性能分析工具

### 1. 搜索耗时统计

创建脚本分析搜索性能：

```bash
#!/bin/bash
# search_performance.sh

echo "=== 搜索性能分析 ==="
echo "FlowID: $1"

echo "1. 总体搜索耗时:"
adb logcat -d | grep "FlowID:$1" | grep "SEARCH_CONTENT_COMPLETE" | grep -o "总耗时=[0-9]*ms"

echo "2. 各站点搜索耗时:"
adb logcat -d | grep "FlowID:$1" | grep "SPIDER_SEARCH_SUCCESS" | grep -o "\[.*\] 耗时:[0-9]*ms"

echo "3. 搜索结果统计:"
adb logcat -d | grep "FlowID:$1" | grep "SEARCH_RESULT_RECEIVED" | grep -o "原始结果数=[0-9]*"
```

### 2. 搜索成功率统计

```bash
#!/bin/bash
# search_success_rate.sh

echo "=== 搜索成功率分析 ==="

TOTAL_SITES=$(adb logcat -d | grep "SINGLE_SITE_SEARCH" | wc -l)
SUCCESS_SITES=$(adb logcat -d | grep "SPIDER_SEARCH_SUCCESS" | wc -l)
ERROR_SITES=$(adb logcat -d | grep "SPIDER_SEARCH_ERROR" | wc -l)

echo "总搜索站点: $TOTAL_SITES"
echo "成功站点: $SUCCESS_SITES"
echo "失败站点: $ERROR_SITES"
echo "成功率: $(echo "scale=2; $SUCCESS_SITES * 100 / $TOTAL_SITES" | bc)%"
```

## 🎯 最佳实践

### 1. 日志监控建议

- **实时监控**：使用 `adb logcat | grep "VOD_FLOW"` 实时查看搜索日志
- **FlowID跟踪**：通过FlowID跟踪完整的搜索链路
- **性能关注**：重点关注搜索耗时超过2秒的站点
- **错误处理**：及时处理搜索错误，避免影响用户体验

### 2. 问题定位流程

1. **确定FlowID**：从搜索开始日志中获取FlowID
2. **跟踪完整链路**：使用FlowID过滤所有相关日志
3. **分析关键节点**：重点分析耗时长或出错的节点
4. **定位根本原因**：结合站点配置和网络状况分析问题

### 3. 性能优化建议

- **并发控制**：合理设置线程池大小（当前为10）
- **超时设置**：为慢速站点设置合理的超时时间
- **缓存机制**：对搜索结果进行适当缓存
- **站点管理**：及时禁用不可用的站点

## 📚 总结

通过完善的日志跟踪系统，TV-Vod的搜索功能现在具备了：

1. **完整的可观测性**：从搜索开始到结果选择的全链路跟踪
2. **详细的性能监控**：每个阶段的耗时和成功率统计
3. **强大的问题排查能力**：通过FlowID快速定位问题
4. **多维度的分析工具**：支持性能分析和成功率统计

这套日志系统为开发者提供了强大的调试和优化工具，能够快速识别和解决搜索相关的问题，提升用户体验。
