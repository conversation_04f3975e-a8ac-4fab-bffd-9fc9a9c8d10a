<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingStart="24dp"
    android:paddingTop="16dp"
    android:paddingEnd="24dp">

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/input"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hintEnabled="false">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="socks5://127.0.0.1:9978"
            android:imeOptions="actionDone"
            android:importantForAutofill="no"
            android:inputType="text"
            android:singleLine="true" />

    </com.google.android.material.textfield.TextInputLayout>
</LinearLayout>