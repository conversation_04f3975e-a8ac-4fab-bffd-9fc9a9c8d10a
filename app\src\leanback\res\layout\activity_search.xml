<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="true"
    android:keepScreenOn="true"
    android:orientation="horizontal"
    android:paddingStart="24dp"
    android:paddingTop="24dp"
    android:paddingEnd="24dp">

    <LinearLayout
        android:id="@+id/recordLayout"
        android:layout_width="128dp"
        android:layout_height="match_parent"
        android:layout_marginEnd="24dp"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:text="@string/search_record"
            android:textColor="@color/white"
            android:textSize="24sp" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recordRecycler"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:overScrollMode="never"
            android:paddingBottom="24dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:itemCount="10"
            tools:listitem="@layout/adapter_search_record" />

    </LinearLayout>

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginEnd="24dp">

        <com.fongmi.android.tv.ui.custom.CustomMic
            android:id="@+id/mic"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:layout_marginEnd="12dp"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:nextFocusLeft="@id/record"
            android:scaleType="fitCenter"
            android:src="@drawable/ic_search_mic" />

        <com.fongmi.android.tv.ui.custom.CustomSearchView
            android:id="@+id/keyword"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignEnd="@+id/keyboard"
            android:layout_marginBottom="16dp"
            android:layout_toEndOf="@+id/mic"
            android:background="@null"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:hint="@string/search_keyword"
            android:imeOptions="actionDone"
            android:inputType="textCapWords|textAutoCorrect|textAutoComplete"
            android:letterSpacing="0.02"
            android:maxLength="255"
            android:nextFocusRight="@id/word"
            android:singleLine="true"
            android:textColor="@color/white"
            android:textColorHint="@color/white"
            android:textCursorDrawable="@drawable/shape_cursor"
            android:textSize="24sp" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/keyboard"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/keyword"
            android:overScrollMode="never"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            app:spanCount="7"
            tools:itemCount="36"
            tools:listitem="@layout/adapter_keyboard_text" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginEnd="24dp"
        android:orientation="vertical">

        <TextView
            android:id="@+id/hint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:text="@string/search_hot"
            android:textColor="@color/white"
            android:textSize="24sp" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/wordRecycler"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:overScrollMode="never"
            android:paddingBottom="24dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:itemCount="10"
            tools:listitem="@layout/adapter_search_word" />

    </LinearLayout>
</LinearLayout>

