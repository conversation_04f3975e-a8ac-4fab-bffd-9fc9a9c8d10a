<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:gravity="center"
    android:orientation="vertical">

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:contentDescription="@null"
        android:src="@drawable/customactivityoncrash_error_image" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:gravity="center"
        android:lineSpacingExtra="4dp"
        android:text="@string/crash_info"
        android:textColor="@color/grey_200"
        android:textSize="16sp"
        android:textStyle="bold" />

    <Button
        android:id="@+id/restart"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:backgroundTint="?android:attr/colorControlActivated"
        android:text="@string/crash_restart"
        android:textColor="@color/white" />

    <Button
        android:id="@+id/details"
        style="?borderlessButtonStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/crash_details"
        android:textColor="@color/grey_200" />

</LinearLayout>