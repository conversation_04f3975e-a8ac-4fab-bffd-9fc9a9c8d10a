<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/transparent"
        android:elevation="0dp"
        app:elevation="0dp"
        app:liftOnScrollColor="@color/transparent">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:text="@string/nav_setting"
            android:textColor="@color/white"
            android:textSize="20sp"
            android:textStyle="bold"
            app:layout_scrollFlags="scroll|enterAlways" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:overScrollMode="never"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:paddingStart="16dp"
            android:paddingTop="16dp"
            android:paddingEnd="16dp"
            android:paddingBottom="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <LinearLayout
                    android:id="@+id/vod"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="12dp"
                    android:layout_weight="1"
                    android:background="@drawable/shape_item"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="16dp"
                        android:text="@string/setting_vod"
                        android:textColor="@color/white"
                        android:textSize="16sp" />

                    <TextView
                        android:id="@+id/vodUrl"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="middle"
                        android:gravity="end"
                        android:singleLine="true"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        tools:text="https://" />

                </LinearLayout>

                <ImageView
                    android:id="@+id/vodHome"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="12dp"
                    android:background="@drawable/shape_item"
                    android:padding="7dp"
                    android:scaleType="fitCenter"
                    android:src="@drawable/ic_setting_home" />

                <ImageView
                    android:id="@+id/vodHistory"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/shape_item"
                    android:padding="7dp"
                    android:scaleType="fitCenter"
                    android:src="@drawable/ic_setting_history" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <LinearLayout
                    android:id="@+id/live"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="12dp"
                    android:layout_weight="1"
                    android:background="@drawable/shape_item"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="16dp"
                        android:text="@string/setting_live"
                        android:textColor="@color/white"
                        android:textSize="16sp" />

                    <TextView
                        android:id="@+id/liveUrl"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="middle"
                        android:gravity="end"
                        android:singleLine="true"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        tools:text="https://" />

                </LinearLayout>

                <ImageView
                    android:id="@+id/liveHome"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="12dp"
                    android:background="@drawable/shape_item"
                    android:padding="7dp"
                    android:scaleType="fitCenter"
                    android:src="@drawable/ic_setting_home" />

                <ImageView
                    android:id="@+id/liveHistory"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/shape_item"
                    android:padding="7dp"
                    android:scaleType="fitCenter"
                    android:src="@drawable/ic_setting_history" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <LinearLayout
                    android:id="@+id/wall"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="12dp"
                    android:layout_weight="1"
                    android:background="@drawable/shape_item"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="16dp"
                        android:text="@string/setting_wall"
                        android:textColor="@color/white"
                        android:textSize="16sp" />

                    <TextView
                        android:id="@+id/wallUrl"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="middle"
                        android:gravity="end"
                        android:singleLine="true"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        tools:text="https://" />

                </LinearLayout>

                <ImageView
                    android:id="@+id/wallDefault"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="12dp"
                    android:background="@drawable/shape_item"
                    android:padding="7dp"
                    android:scaleType="fitCenter"
                    android:src="@drawable/ic_setting_home" />

                <ImageView
                    android:id="@+id/wallRefresh"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/shape_item"
                    android:padding="7dp"
                    android:scaleType="fitCenter"
                    android:src="@drawable/ic_setting_refresh" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/player"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/shape_item"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/setting_player"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/incognito"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/shape_item"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:text="@string/setting_incognito"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/incognitoText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    tools:text="Off" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/size"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/shape_item"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:text="@string/setting_size"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/sizeText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    tools:text="Medium" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/doh"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/shape_item"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:text="@string/setting_doh"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/dohText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    tools:text="Google" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/proxy"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/shape_item"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:text="@string/setting_proxy"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/proxyText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    tools:text="http" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/cache"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/shape_item"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:text="@string/setting_cache"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/cacheText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    tools:text="1.0 MB" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/shape_item"
                android:orientation="horizontal"
                android:padding="0dp">

                <TextView
                    android:id="@+id/backup"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="?android:attr/selectableItemBackground"
                    android:paddingStart="12dp"
                    android:paddingTop="8dp"
                    android:paddingEnd="12dp"
                    android:paddingBottom="8dp"
                    android:text="@string/setting_backup"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/restore"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="?android:attr/selectableItemBackground"
                    android:gravity="end"
                    android:paddingStart="12dp"
                    android:paddingTop="8dp"
                    android:paddingEnd="12dp"
                    android:paddingBottom="8dp"
                    android:text="@string/setting_restore"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/version"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/shape_item"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:text="@string/setting_version"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/versionText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    tools:text="1.2.1" />

            </LinearLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.coordinatorlayout.widget.CoordinatorLayout>