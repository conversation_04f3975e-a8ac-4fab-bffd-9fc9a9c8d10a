# TV-Vod 播放地址解析日志跟踪系统

## 概述

本文档详细说明了为TV-Vod项目添加的完整播放地址解析日志跟踪系统。该系统能够跟踪从用户点击电影卡片到播放器实际播放视频的整个流程，特别关注播放线路解析和URL获取过程。

## 完整流程跟踪

### 1. 电影卡片点击 → 电影详情获取

**流程路径：**
```
VodFragment.onItemClick() → VideoActivity.start() → VideoActivity.getDetail() → SiteViewModel.detailContent()
```

**日志标识：**
- `[MOVIE_CLICK]` - 电影卡片点击
- `[DETAIL_CONTENT]` - 详情内容获取
- `[DETAIL_CONTENT_START]` - 开始获取详情
- `[DETAIL_CONTENT_SITE]` - 站点信息
- `[DETAIL_CONTENT_COMPLETE]` - 详情获取完成

### 2. 集数选择 → 播放地址获取

**流程路径：**
```
VideoActivity.setEpisodeActivated() → VideoActivity.getPlayer() → SiteViewModel.playerContent()
```

**日志标识：**
- `[EPISODE_SELECT]` - 集数选择
- `[PLAYER_CONTENT_START]` - 开始获取播放地址
- `[PLAYER_CONTENT_SITE]` - 站点信息
- `[PLAYER_CONTENT_JS]` - JavaScript Spider获取
- `[PLAYER_CONTENT_HTTP]` - HTTP API获取
- `[PLAYER_CONTENT_COMPLETE]` - 播放地址获取完成

### 3. URL解析处理

**流程路径：**
```
ParseJob.start() → ParseJob.doInBackground() → 各种解析方法
```

**日志标识：**
- `[PARSE_START]` - 开始URL解析
- `[PARSE_CONFIG]` - 解析器配置
- `[PARSE_TYPE]` - 解析类型
- `[PARSE_SNIFF]` - 嗅探解析
- `[PARSE_JSON]` - JSON解析
- `[PARSE_SUCCESS]` - 解析成功
- `[PARSE_ERROR]` - 解析失败

### 4. 播放器启动

**流程路径：**
```
Players.start() → Players.setMediaItem()
```

**日志标识：**
- `[PLAYER_START]` - 播放器开始
- `[PLAYER_PARSE]` - 需要解析
- `[PLAYER_DIRECT]` - 直接播放
- `[PLAYER_ERROR]` - 播放错误

## 核心修改文件

### 1. SiteViewModel.java
- 添加了 `currentFlowId` 字段
- 在 `detailContent()` 方法中添加详细的日志跟踪
- 在 `playerContent()` 方法中添加播放地址获取的完整日志

### 2. ParseJob.java
- 添加了 `flowId` 字段和构造方法
- 在 `start()` 方法中添加解析开始日志
- 在 `doInBackground()` 方法中添加解析类型日志
- 在回调方法中添加成功/失败日志

### 3. Players.java
- 添加了 `flowId` 字段和 `setFlowId()` 方法
- 在 `start()` 方法中添加播放器启动日志
- 修改 `startParse()` 方法传递flowId

### 4. VideoActivity.java
- 在 `setPlayer()` 方法中设置播放器的flowId

### 5. FlowLogger.java
- 添加了多个新的日志方法支持播放地址解析跟踪
- 包括详情获取、播放地址获取、URL解析等各个阶段的日志方法

## 日志格式说明

### 标准日志格式
```
[FlowID:xxxxx] [阶段标识] 具体信息
```

### FlowID生成规则
- 正常流程：由VideoActivity生成，格式为 `MOVIE_时间戳`
- 临时流程：当没有FlowID时自动生成，格式为 `TEMP_DETAIL_时间戳`

### 日志级别
- `INFO` - 正常流程信息
- `ERROR` - 错误信息
- `WARN` - 警告信息
- `DEBUG` - 调试信息

## 使用方法

### 1. 查看完整播放流程日志
```bash
adb logcat | grep "VOD_FLOW"
```

### 2. 跟踪特定FlowID的完整流程
```bash
adb logcat | grep "FlowID:MOVIE_12345"
```

### 3. 只查看URL解析相关日志
```bash
adb logcat | grep "PARSE_"
```

### 4. 只查看播放地址获取日志
```bash
adb logcat | grep "PLAYER_CONTENT"
```

## 典型日志输出示例

```
[FlowID:MOVIE_12345] [MOVIE_CLICK] 点击电影: 电影名称, ID: movie123, 站点: site1
[FlowID:MOVIE_12345] [DETAIL_CONTENT_START] 开始获取详情: key=site1, id=movie123
[FlowID:MOVIE_12345] [DETAIL_CONTENT_SITE] 站点信息: 站点名称 (类型:3, API:http://...)
[FlowID:MOVIE_12345] [DETAIL_CONTENT_COMPLETE] 详情获取完成: 影片=电影名称, 线路数=5, 耗时=1200ms
[FlowID:MOVIE_12345] [EPISODE_SELECT] 选择集数: 第1集
[FlowID:MOVIE_12345] [PLAYER_CONTENT_START] 获取播放地址: key=site1, flag=线路1, id=ep001
[FlowID:MOVIE_12345] [PLAYER_CONTENT_JS] JavaScript Spider获取播放地址
[FlowID:MOVIE_12345] [PLAYER_CONTENT_COMPLETE] 播放地址获取完成: URL=http://..., 需要解析=true, 耗时=800ms
[FlowID:MOVIE_12345] [PARSE_START] 开始解析URL: http://..., useParse: true
[FlowID:MOVIE_12345] [PARSE_CONFIG] 解析器配置: 类型=1, 名称=JSON解析器, URL=http://...
[FlowID:MOVIE_12345] [PARSE_JSON] 开始JSON解析
[FlowID:MOVIE_12345] [PARSE_SUCCESS] 解析成功: URL=http://real-video-url.m3u8, From=JSON解析器
[FlowID:MOVIE_12345] [PLAYER_START] 播放器开始: URL=http://real-video-url.m3u8, 需要解析=false, 超时=30000ms
[FlowID:MOVIE_12345] [PLAYER_DIRECT] 直接播放URL: http://real-video-url.m3u8
```

## 故障排查指南

### 1. 播放失败时的日志分析
- 查找 `[PLAYER_ERROR]` 标识的日志
- 检查 `[PARSE_ERROR]` 是否有解析失败
- 确认 `[PLAYER_CONTENT_COMPLETE]` 中的URL是否正确

### 2. 解析超时问题
- 查找解析开始和结束的时间差
- 检查是否有 `[PARSE_SUPER]` 超级解析的日志
- 确认网络请求是否正常

### 3. 站点兼容性问题
- 检查 `[DETAIL_CONTENT_SITE]` 中的站点类型
- 确认 `[PLAYER_CONTENT_JS]` 或 `[PLAYER_CONTENT_HTTP]` 的处理方式
- 查看Spider创建和调用的日志

## 扩展说明

该日志系统已经集成到现有的FlowLogger框架中，可以与其他模块的日志系统协同工作。所有日志都会同时输出到Android系统日志和应用内部日志系统中，便于开发和调试。

通过这个完整的日志跟踪系统，开发者可以清楚地了解每一次播放请求的完整处理过程，快速定位问题并优化性能。
