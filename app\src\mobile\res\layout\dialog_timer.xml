<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:text="@string/play_timer"
        android:textColor="?android:attr/textColorPrimary"
        android:textSize="16sp" />

    <LinearLayout
        android:id="@+id/list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingStart="16dp"
        android:paddingTop="8dp"
        android:paddingEnd="16dp"
        android:paddingBottom="8dp"
        android:visibility="gone"
        tools:visibility="visible">

        <TextView
            android:id="@+id/time1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            android:background="@drawable/shape_accent"
            android:tag="5"
            android:text="@string/timer_5"
            android:textColor="?android:attr/textColorPrimary"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/time2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            android:background="@drawable/shape_accent"
            android:tag="15"
            android:text="@string/timer_15"
            android:textColor="?android:attr/textColorPrimary"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/time3"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            android:background="@drawable/shape_accent"
            android:tag="30"
            android:text="@string/timer_30"
            android:textColor="?android:attr/textColorPrimary"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/time4"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            android:background="@drawable/shape_accent"
            android:tag="60"
            android:text="@string/timer_60"
            android:textColor="?android:attr/textColorPrimary"
            android:textSize="14sp" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/timer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        tools:visibility="visible">

        <TextView
            android:id="@+id/tick"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:layout_marginBottom="24dp"
            android:textColor="?android:attr/textColorPrimary"
            android:textSize="48sp"
            android:textStyle="bold"
            tools:text="5:00" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/delay"
            style="?attr/materialButtonStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:singleLine="true"
            android:text="@string/timer_delay"
            android:textColor="@color/white" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/reset"
            style="?attr/materialButtonOutlinedStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:singleLine="true"
            android:text="@string/timer_cancel"
            android:textColor="?android:attr/textColorPrimary" />

    </LinearLayout>
</LinearLayout>