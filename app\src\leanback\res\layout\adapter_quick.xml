<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/selector_item"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:nextFocusUp="@id/part"
    android:orientation="vertical">

    <TextView
        android:id="@+id/name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="16sp"
        tools:text="蜘蛛人" />

    <TextView
        android:id="@+id/site"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:singleLine="true"
        android:textColor="@color/green_a_400"
        android:textSize="14sp"
        tools:text="泥巴"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/remark"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:singleLine="true"
        android:textColor="@color/yellow_500"
        android:textSize="14sp"
        tools:text="1080p" />

</LinearLayout>