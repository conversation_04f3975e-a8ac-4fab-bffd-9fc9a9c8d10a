<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="56dp"
    android:background="@drawable/selector_group"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:gravity="center"
    android:orientation="horizontal"
    android:paddingStart="20dp"
    android:paddingEnd="20dp">

    <TextView
        android:id="@+id/name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:duplicateParentState="true"
        android:ellipsize="marquee"
        android:singleLine="true"
        android:textColor="@color/group"
        android:textSize="16sp"
        android:textStyle="bold"
        tools:text="收藏" />

</LinearLayout>