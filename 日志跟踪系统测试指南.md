# TV-Vod 播放地址解析日志跟踪系统测试指南

## 测试准备

### 1. 安装应用
```bash
./gradlew assembleDebug
adb install app/build/outputs/apk/leanback/debug/app-leanback-debug.apk
```

### 2. 启动日志监控
```bash
# 清除之前的日志
adb logcat -c

# 开始监控VOD流程日志
adb logcat | grep "VOD_FLOW"
```

## 测试场景

### 场景1：完整播放流程测试

**操作步骤：**
1. 打开TV-Vod应用
2. 选择一个电影卡片点击
3. 等待电影详情页面加载
4. 选择一个播放线路
5. 选择一个集数（如第1集）
6. 观察播放器启动过程

**预期日志输出：**
```
=== [FlowID:MOVIE_12345] === 电影点击开始 ===
[FlowID:MOVIE_12345] [MOVIE_CLICK] 点击电影 [site1] 电影名称，来源: VodFragment
[FlowID:MOVIE_12345] [ID_CHECK] 检查电影ID: 原始ID=movie123
[FlowID:MOVIE_12345] [ID_NORMAL] 正常ID处理: movie123 (获取详情)
=== [FlowID:MOVIE_12345] === 开始获取电影详情 ===
[FlowID:MOVIE_12345] [DETAIL_CONTENT_START] 开始获取详情: key=site1, id=movie123
[FlowID:MOVIE_12345] [DETAIL_CONTENT_SITE] 站点信息: 站点名称 (类型:3, API:http://...)
[FlowID:MOVIE_12345] [DETAIL_CONTENT_COMPLETE] 详情获取完成: 影片=电影名称, 线路数=5, 耗时=1200ms
[FlowID:MOVIE_12345] [EPISODE_SELECT] 选择集数: 第1集
[FlowID:MOVIE_12345] [PLAYER_CONTENT_START] 获取播放地址: key=site1, flag=线路1, id=ep001
[FlowID:MOVIE_12345] [PLAYER_CONTENT_JS] JavaScript Spider获取播放地址
[FlowID:MOVIE_12345] [PLAYER_CONTENT_COMPLETE] 播放地址获取完成: URL=http://..., 需要解析=true, 耗时=800ms
=== [FlowID:MOVIE_12345] === 播放器开始播放 ===
[FlowID:MOVIE_12345] [PLAYER_START] 播放器开始: URL=http://..., 需要解析=true, 超时=30000ms
[FlowID:MOVIE_12345] [PLAYER_PARSE] 需要解析，启动解析器
[FlowID:MOVIE_12345] [PARSE_START] 开始解析URL: http://..., useParse: true
[FlowID:MOVIE_12345] [PARSE_CONFIG] 解析器配置: 类型=1, 名称=JSON解析器, URL=http://...
[FlowID:MOVIE_12345] [PARSE_JSON] 开始JSON解析
[FlowID:MOVIE_12345] [PARSE_SUCCESS] 解析成功: URL=http://real-video-url.m3u8, From=JSON解析器
```

### 场景2：不同解析类型测试

**测试目标：** 验证不同解析类型的日志输出

**操作：** 选择需要不同解析方式的视频源

**预期日志：**
```
# 嗅探解析 (Type 0)
[FlowID:MOVIE_12345] [PARSE_SNIFF] 开始嗅探解析

# JSON解析 (Type 1)
[FlowID:MOVIE_12345] [PARSE_JSON] 开始JSON解析

# JSON扩展解析 (Type 2)
[FlowID:MOVIE_12345] [PARSE_JSON_EXT] 开始JSON扩展解析

# JSON聚合解析 (Type 3)
[FlowID:MOVIE_12345] [PARSE_JSON_MIX] 开始JSON聚合解析

# 超级解析 (Type 4)
[FlowID:MOVIE_12345] [PARSE_SUPER] 开始超级解析
```

### 场景3：错误处理测试

**测试目标：** 验证错误情况下的日志输出

**操作：** 选择无效或失效的视频源

**预期日志：**
```
[FlowID:MOVIE_12345] [PARSE_ERROR] 解析失败
[FlowID:MOVIE_12345] [PLAYER_ERROR] 非法URL: invalid_url
[FlowID:MOVIE_12345] [PLAYER_ERROR] DRM不支持
```

### 场景4：直接播放测试

**测试目标：** 验证不需要解析的直接播放

**操作：** 选择直接播放链接的视频源

**预期日志：**
```
[FlowID:MOVIE_12345] [PLAYER_DIRECT] 直接播放URL: http://direct-video-url.mp4
```

## 日志分析工具

### 1. 按FlowID过滤
```bash
# 跟踪特定播放请求的完整流程
adb logcat | grep "FlowID:MOVIE_12345"
```

### 2. 按阶段过滤
```bash
# 只看详情获取阶段
adb logcat | grep "DETAIL_CONTENT"

# 只看播放地址获取阶段
adb logcat | grep "PLAYER_CONTENT"

# 只看URL解析阶段
adb logcat | grep "PARSE_"

# 只看播放器启动阶段
adb logcat | grep "PLAYER_START\|PLAYER_PARSE\|PLAYER_DIRECT\|PLAYER_ERROR"
```

### 3. 性能分析
```bash
# 查看耗时信息
adb logcat | grep "耗时"

# 查看完成状态
adb logcat | grep "COMPLETE"
```

### 4. 错误排查
```bash
# 查看所有错误
adb logcat | grep "ERROR\|失败"

# 查看解析失败
adb logcat | grep "PARSE_ERROR"

# 查看播放器错误
adb logcat | grep "PLAYER_ERROR"
```

## 验证清单

### ✅ 基础功能验证
- [ ] 电影点击时生成FlowID
- [ ] FlowID在整个流程中保持一致
- [ ] 详情获取过程有完整日志
- [ ] 播放地址获取过程有完整日志
- [ ] URL解析过程有详细日志
- [ ] 播放器启动过程有日志记录

### ✅ 解析类型验证
- [ ] 嗅探解析日志正确
- [ ] JSON解析日志正确
- [ ] JSON扩展解析日志正确
- [ ] JSON聚合解析日志正确
- [ ] 超级解析日志正确

### ✅ 错误处理验证
- [ ] 解析失败时有错误日志
- [ ] 播放器错误时有错误日志
- [ ] 网络错误时有相应日志
- [ ] DRM不支持时有错误日志

### ✅ 性能监控验证
- [ ] 详情获取耗时记录
- [ ] 播放地址获取耗时记录
- [ ] URL解析耗时记录
- [ ] 整体播放启动耗时记录

## 常见问题排查

### 1. 看不到日志输出
**可能原因：**
- 日志级别设置问题
- 应用没有正确安装
- FlowID传递中断

**解决方法：**
```bash
# 检查应用是否运行
adb shell ps | grep tv.vod

# 检查日志级别
adb shell setprop log.tag.VOD_FLOW VERBOSE

# 重新安装应用
./gradlew clean assembleDebug
adb install -r app/build/outputs/apk/leanback/debug/app-leanback-debug.apk
```

### 2. FlowID不一致
**检查方法：**
```bash
# 查看FlowID生成和传递
adb logcat | grep "FlowID:" | head -20
```

### 3. 解析过程没有日志
**检查方法：**
```bash
# 确认是否进入解析流程
adb logcat | grep "PARSE_START\|PLAYER_PARSE"
```

## 日志保存

### 保存完整测试日志
```bash
# 保存到文件
adb logcat | grep "VOD_FLOW" > vod_flow_test.log

# 带时间戳保存
adb logcat -v time | grep "VOD_FLOW" > vod_flow_test_$(date +%Y%m%d_%H%M%S).log
```

通过这个测试指南，您可以全面验证播放地址解析日志跟踪系统的功能，确保每个播放请求都能被完整跟踪和分析。
