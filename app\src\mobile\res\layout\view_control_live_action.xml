<?xml version="1.0" encoding="utf-8"?>
<HorizontalScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:fillViewport="true"
    android:paddingStart="8dp"
    android:paddingEnd="8dp"
    android:scrollbars="none">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/player"
            style="@style/Control.Action"
            android:text="@string/play_exo" />

        <TextView
            android:id="@+id/decode"
            style="@style/Control.Action"
            tools:text="硬解" />

        <TextView
            android:id="@+id/speed"
            style="@style/Control.Action"
            android:visibility="gone"
            tools:text="速度"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/scale"
            style="@style/Control.Action"
            tools:text="縮放" />

        <TextView
            android:id="@+id/home"
            style="@style/Control.Action"
            tools:text="首頁" />

        <TextView
            android:id="@+id/line"
            style="@style/Control.Action"
            tools:text="來源 1" />

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/text"
            style="@style/Control.Action"
            android:tag="3"
            android:text="@string/play_track_text"
            android:visibility="gone"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/audio"
            style="@style/Control.Action"
            android:tag="1"
            android:text="@string/play_track_audio"
            android:visibility="gone"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/video"
            style="@style/Control.Action"
            android:tag="2"
            android:text="@string/play_track_video"
            android:visibility="gone"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/invert"
            style="@style/Control.Action"
            android:text="@string/play_invert"
            android:textColor="@color/text" />

        <TextView
            android:id="@+id/across"
            style="@style/Control.Action"
            android:text="@string/play_across"
            android:textColor="@color/text" />

        <TextView
            android:id="@+id/change"
            style="@style/Control.Action"
            android:text="@string/play_change"
            android:textColor="@color/text" />

    </LinearLayout>
</HorizontalScrollView>