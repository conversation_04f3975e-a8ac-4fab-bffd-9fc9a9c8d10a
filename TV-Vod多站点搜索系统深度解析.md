# TV-Vod 多站点搜索系统深度解析

## 🎯 概述

本文档深入分析TV-Vod项目的多站点搜索系统，详细解释从用户点击电影到获取真实播放内容的完整流程，包括多站点搜索和单站点搜索的具体实现机制。

## 🏗️ 搜索系统架构

### 核心组件

1. **VideoActivity** - 搜索控制器，管理搜索流程
2. **SiteViewModel** - 业务逻辑层，处理搜索请求
3. **Site** - 站点配置，定义站点属性和能力
4. **Spider** - 爬虫引擎，执行具体搜索逻辑
5. **SpiderWrapper** - 爬虫包装器，提供日志和错误处理
6. **Result** - 搜索结果，统一的数据格式

### 站点类型分类

| 类型 | 名称 | 实现方式 | 搜索机制 |
|------|------|----------|----------|
| 0 | XML API | HTTP请求 | 标准XML接口搜索 |
| 1 | JSON API | HTTP请求 | 标准JSON接口搜索 |
| 2 | JSON扩展 | HTTP请求 | 扩展JSON接口搜索 |
| 3 | JavaScript Spider | JS引擎 | JavaScript脚本搜索 |
| 4 | HTTP API扩展 | HTTP请求 | 高级HTTP接口搜索 |
| Python | Python Spider | Python引擎 | Python脚本搜索 |

## 🔍 多站点搜索详细流程

### 第一阶段：搜索初始化

#### 1.1 触发搜索 (initSearch)
**位置：** `VideoActivity.initSearch()`
**触发条件：** 
- 检测到msearch协议
- 用户手动搜索
- 自动搜索模式

**关键操作：**
```java
private void initSearch(String keyword, boolean auto) {
    // 生成搜索FlowID
    String flowId = getFlowId();
    
    // 记录搜索初始化
    FlowLogger.logSearchInit(flowId, keyword, auto);
    
    // 停止之前的搜索
    stopSearch();
    
    // 设置搜索模式
    setAutoMode(auto);
    setInitAuto(auto);
    
    // 启动多站点搜索
    startSearch(keyword);
}
```

**日志输出：**
```
[FlowID:MOVIE_12345] [SEARCH_INIT] 初始化搜索: 关键词=朝雪录，自动模式=true
```

#### 1.2 站点筛选 (startSearch)
**位置：** `VideoActivity.startSearch()`
**筛选条件：**
- 站点必须可搜索 (`site.isSearchable()`)
- 自动模式下站点必须可切换 (`site.isChangeable()`)

**关键操作：**
```java
private void startSearch(String keyword) {
    mQuickAdapter.clear();
    List<Site> sites = new ArrayList<>();
    
    // 创建线程池（10个并发线程）
    mExecutor = Executors.newFixedThreadPool(10);
    
    // 筛选可用站点
    for (Site site : VodConfig.get().getSites()) {
        if (isPass(site)) sites.add(site);
    }
    
    // 记录多站点搜索开始
    FlowLogger.logMultiSiteSearchStart(flowId, keyword, sites.size());
    
    // 为每个站点启动搜索线程
    for (Site site : sites) {
        mExecutor.execute(() -> search(site, keyword));
    }
}
```

**日志输出：**
```
[FlowID:MOVIE_12345] [MULTI_SITE_SEARCH_START] 开始多站点搜索: 关键词=朝雪录，站点数=15
```

### 第二阶段：单站点搜索执行

#### 2.1 单站点搜索调度 (search)
**位置：** `VideoActivity.search()`
**关键操作：**
```java
private void search(Site site, String keyword) {
    try {
        // 记录站点详细信息
        FlowLogger.logVodSiteInfo(flowId, site.getKey(), site.getName(), 
            site.getType(), site.getApi(), site.isSearchable(), site.isQuickSearch());
        
        // 设置ViewModel的FlowID
        mViewModel.setFlowId(flowId);
        
        // 执行搜索
        mViewModel.searchContent(site, keyword, true);
    } catch (Throwable e) {
        FlowLogger.logVodSearchError(flowId, site.getKey(), e.getMessage());
    }
}
```

**日志输出：**
```
[FlowID:MOVIE_12345] [SINGLE_SITE_SEARCH] 执行单站点搜索: 关键词=朝雪录，站点=site1(影视站点), 类型=3, 支持快速搜索=true
[FlowID:MOVIE_12345] [SITE_INFO] 站点详情: API=http://api.example.com, 可搜索=true, 可切换=true
```

#### 2.2 SiteViewModel搜索处理 (searchContent)
**位置：** `SiteViewModel.searchContent()`
**处理流程：**

**JavaScript Spider搜索 (类型3)：**
```java
if (site.getType() == 3) {
    // 检查快速搜索支持
    if (quick && !site.isQuickSearch()) return;
    
    // 调用Spider搜索
    String searchContent = site.spider(flowId).searchContent(translatedKeyword, quick);
    
    // 解析JSON结果
    Result result = Result.fromJson(searchContent);
    
    // 发布结果
    post(site, result);
}
```

**HTTP API搜索 (类型0,1,2,4)：**
```java
else {
    // 构建请求参数
    ArrayMap<String, String> params = new ArrayMap<>();
    params.put("wd", translatedKeyword);
    params.put("quick", String.valueOf(quick));
    
    // 发送HTTP请求
    String searchContent = call(site, params);
    
    // 解析结果（XML或JSON）
    Result result = fetchPic(site, Result.fromType(site.getType(), searchContent));
    
    // 发布结果
    post(site, result);
}
```

**日志输出：**
```
[FlowID:MOVIE_12345] [SEARCH_CONTENT_START] 开始搜索内容: 站点=site1(影视站点), 关键词=朝雪录→朝雪录, 快速搜索=true
[FlowID:MOVIE_12345] [SEARCH_JS_SPIDER] JavaScript Spider搜索: 站点=site1
[FlowID:MOVIE_12345] [SEARCH_JS_RESPONSE] JavaScript Spider响应: 站点=site1, 耗时=800ms, 数据长度=2048
[FlowID:MOVIE_12345] [SEARCH_RESULT_PARSE] 解析搜索结果: 站点=site1, 结果数=3
[FlowID:MOVIE_12345] [SEARCH_CONTENT_COMPLETE] 搜索完成: 站点=site1, 总耗时=850ms
```

#### 2.3 Spider层搜索执行
**位置：** `SpiderWrapper.searchContent()`
**JavaScript Spider执行：**
```java
public String searchContent(String key, boolean quick) throws Exception {
    // 调用JavaScript函数
    String result = spider.searchContent(key, quick);
    
    // 解析JSON结果统计
    if (result != null && result.trim().startsWith("{")) {
        JsonObject jsonResult = JsonParser.parseString(result).getAsJsonObject();
        if (jsonResult.has("list")) {
            JsonArray list = jsonResult.getAsJsonArray("list");
            // 记录结果数量
        }
    }
    
    return result;
}
```

**日志输出：**
```
[FlowID:MOVIE_12345] [SPIDER_SEARCH_START] Spider搜索开始 [site1] 关键词:朝雪录, 快速搜索:true
[FlowID:MOVIE_12345] [SPIDER_SEARCH_SUCCESS] Spider搜索成功 [site1] 耗时:750ms, 数据长度:2048
[FlowID:MOVIE_12345] [SPIDER_SEARCH_PARSE] Spider搜索解析 [site1] JSON结果数量:3
```

### 第三阶段：搜索结果处理

#### 3.1 结果接收和过滤 (setSearch)
**位置：** `VideoActivity.setSearch()`
**处理流程：**
```java
private void setSearch(Result result) {
    List<Vod> items = result.getList();
    int originalCount = items.size();
    String siteKey = items.get(0).getSiteKey();
    
    // 记录结果接收
    FlowLogger.logVodSearchResultReceived(flowId, siteKey, originalCount);
    
    // 过滤不匹配的结果
    Iterator<Vod> iterator = items.iterator();
    int filteredCount = 0;
    while (iterator.hasNext()) {
        if (mismatch(iterator.next())) {
            iterator.remove();
            filteredCount++;
        }
    }
    
    // 记录过滤结果
    FlowLogger.logVodSearchResultFilter(flowId, siteKey, originalCount, filteredCount, items.size());
    
    // 添加到适配器
    mQuickAdapter.addAll(items);
    
    // 记录结果详情
    for (int i = 0; i < Math.min(items.size(), 3); i++) {
        Vod vod = items.get(i);
        // 记录每个结果的详细信息
    }
}
```

**日志输出：**
```
[FlowID:MOVIE_12345] [SEARCH_RESULT_RECEIVED] 接收搜索结果: 站点=影视站点(site1), 原始结果数=3
[FlowID:MOVIE_12345] [SEARCH_RESULT_FILTER] 结果过滤: 站点=site1, 原始=3, 过滤掉=0, 最终=3
[FlowID:MOVIE_12345] [SEARCH_RESULT_ADD] 添加到适配器: 站点=site1, 数量=3, 当前总数=3
[FlowID:MOVIE_12345] [SEARCH_RESULT_DETAIL] 结果详情[1]: ID=12345, 名称=朝雪录, 年份=2024
[FlowID:MOVIE_12345] [SEARCH_RESULT_DETAIL] 结果详情[2]: ID=12346, 名称=朝雪录 第二季, 年份=2024
[FlowID:MOVIE_12345] [SEARCH_RESULT_DETAIL] 结果详情[3]: ID=12347, 名称=朝雪录 电影版, 年份=2024
```

#### 3.2 自动选择最佳结果 (nextSite)
**位置：** `VideoActivity.nextSite()`
**选择策略：**
- 优先选择名称完全匹配的结果
- 考虑年份、类型等因素
- 选择评分最高的结果

**日志输出：**
```
[FlowID:MOVIE_12345] [AUTO_SELECT_RESULT] 自动选择搜索结果: 电影=朝雪录，真实ID=12345，站点=site1
```

## 🔧 搜索机制详细分析

### JavaScript Spider搜索机制

**执行环境：** QuickJS JavaScript引擎
**搜索函数：** `search(key, quick)`
**返回格式：** JSON字符串

**典型搜索脚本结构：**
```javascript
function search(key, quick) {
    // 1. 构建搜索URL
    let searchUrl = `${baseUrl}/search?q=${encodeURIComponent(key)}`;
    
    // 2. 发送HTTP请求
    let response = req(searchUrl);
    
    // 3. 解析HTML/JSON响应
    let $ = load(response);
    let results = [];
    
    // 4. 提取搜索结果
    $('.movie-item').each(function() {
        results.push({
            vod_id: $(this).attr('data-id'),
            vod_name: $(this).find('.title').text(),
            vod_pic: $(this).find('img').attr('src'),
            vod_year: $(this).find('.year').text()
        });
    });
    
    // 5. 返回JSON结果
    return JSON.stringify({
        list: results
    });
}
```

### HTTP API搜索机制

**请求方式：** GET/POST
**参数格式：** URL参数或表单数据
**返回格式：** XML或JSON

**典型API请求：**
```
GET /api.php?ac=detail&wd=朝雪录&quick=true
```

**XML响应格式：**
```xml
<rss>
    <list>
        <video>
            <id>12345</id>
            <name>朝雪录</name>
            <pic>http://example.com/pic.jpg</pic>
            <year>2024</year>
        </video>
    </list>
</rss>
```

**JSON响应格式：**
```json
{
    "list": [
        {
            "vod_id": "12345",
            "vod_name": "朝雪录",
            "vod_pic": "http://example.com/pic.jpg",
            "vod_year": "2024"
        }
    ]
}
```

### Python Spider搜索机制

**执行环境：** Chaquopy Python引擎
**搜索函数：** `searchContent(key, quick, pg)`
**返回格式：** JSON字符串

**典型搜索脚本结构：**
```python
def searchContent(self, key, quick, pg="1"):
    # 1. 构建搜索URL
    search_url = f"{self.base_url}/search?q={key}&page={pg}"
    
    # 2. 发送HTTP请求
    response = self.fetch(search_url)
    
    # 3. 解析响应
    soup = BeautifulSoup(response.text, 'html.parser')
    results = []
    
    # 4. 提取搜索结果
    for item in soup.select('.movie-item'):
        results.append({
            'vod_id': item.get('data-id'),
            'vod_name': item.select_one('.title').text,
            'vod_pic': item.select_one('img')['src'],
            'vod_year': item.select_one('.year').text
        })
    
    # 5. 返回JSON结果
    return json.dumps({
        'list': results
    }, ensure_ascii=False)
```

## 📊 性能监控和优化

### 关键性能指标

1. **搜索响应时间**
   - 单站点搜索：< 2000ms
   - 多站点搜索总时间：< 5000ms
   - 超时阈值：30000ms

2. **搜索成功率**
   - 目标成功率：> 80%
   - 结果匹配率：> 60%

3. **并发性能**
   - 最大并发站点：10个
   - 线程池大小：10

### 错误处理机制

1. **网络超时**
   - 自动重试机制
   - 降级到其他站点

2. **解析错误**
   - 容错解析
   - 日志记录详细错误信息

3. **站点不可用**
   - 跳过不可用站点
   - 继续其他站点搜索

## 🎯 总结

TV-Vod的多站点搜索系统是一个高度并发、容错性强的分布式搜索架构。通过FlowID机制实现了完整的搜索链路跟踪，支持JavaScript、Python、HTTP API等多种搜索方式，能够在多个视频站点中快速找到用户需要的内容。

系统的核心优势：
1. **高并发**：支持多站点并行搜索
2. **多样性**：支持多种Spider类型
3. **智能化**：自动选择最佳搜索结果
4. **可观测**：完整的日志跟踪链路
5. **容错性**：强大的错误处理机制
