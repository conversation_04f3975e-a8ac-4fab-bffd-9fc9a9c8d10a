<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="16dp">

    <FrameLayout
        android:id="@+id/frame"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:foreground="@drawable/shape_vod">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/image"
            android:layout_width="90dp"
            android:layout_height="120dp"
            android:background="@color/black_10"
            android:scaleType="center"
            app:shapeAppearanceOverlay="@style/Vod.Grid"
            tools:src="@drawable/ic_img_loading" />

        <include
            android:id="@+id/progress"
            layout="@layout/view_progress"
            android:visibility="gone" />

        <FrameLayout
            android:id="@+id/play"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:background="@drawable/shape_control">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/exo_icon_play" />

        </FrameLayout>
    </FrameLayout>

    <TextView
        android:id="@+id/name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_toEndOf="@+id/frame"
        android:ellipsize="end"
        android:maxLines="3"
        android:textColor="?android:attr/textColorPrimary"
        android:textSize="16sp"
        tools:text="狂飆" />

    <TextView
        android:id="@+id/from"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/name"
        android:layout_alignStart="@+id/name"
        android:layout_marginTop="2dp"
        android:singleLine="true"
        android:textColor="?android:attr/textColorSecondary"
        android:textSize="14sp"
        tools:text="Xiaomi 13" />

</RelativeLayout>