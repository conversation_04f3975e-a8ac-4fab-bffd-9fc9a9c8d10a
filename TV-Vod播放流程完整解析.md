# TV-Vod 播放流程完整解析

## 概述

本文档详细描述了TV-Vod项目中从用户点击电影卡片到视频开始播放的完整流程，包括URL解析和获取的每一个步骤。

## 完整流程图

```
用户点击电影卡片
    ↓
VodFragment.onItemClick()
    ↓
VideoActivity.start() [生成FlowID]
    ↓
VideoActivity.onCreate() → initView() → checkId()
    ↓
VideoActivity.getDetail()
    ↓
SiteViewModel.detailContent() [获取电影详情]
    ↓
VideoActivity.setDetail() [显示详情页面]
    ↓
用户选择播放线路和集数
    ↓
VideoActivity.getPlayer()
    ↓
SiteViewModel.playerContent() [获取播放地址]
    ↓
VideoActivity.setPlayer()
    ↓
Players.start() [启动播放器]
    ↓
ParseJob.start() [URL解析] (如果需要)
    ↓
Players.setMediaItem() [设置媒体项]
    ↓
ExoPlayer开始播放
```

## 详细步骤解析

### 第一阶段：电影点击和初始化

#### 1.1 用户点击电影卡片
**位置：** `VodFragment.onItemClick()`
**作用：** 响应用户点击，启动VideoActivity

#### 1.2 VideoActivity启动
**位置：** `VideoActivity.start()`
**关键操作：**
- 生成唯一FlowID：`MOVIE_` + 时间戳
- 记录电影点击日志
- 传递电影信息到VideoActivity

**日志输出：**
```
=== [FlowID:MOVIE_12345] === 电影点击开始 ===
[FlowID:MOVIE_12345] [MOVIE_CLICK] 点击电影 [site1] 电影名称，来源: VodFragment
```

#### 1.3 VideoActivity初始化
**位置：** `VideoActivity.onCreate() → initView() → checkId()`
**关键操作：**
- 检查和处理电影ID
- 处理特殊协议（如push://）
- 决定是否需要搜索模式

**日志输出：**
```
[FlowID:MOVIE_12345] [ID_CHECK] 检查电影ID: 原始ID=movie123
[FlowID:MOVIE_12345] [ID_NORMAL] 正常ID处理: movie123 (获取详情)
```

### 第二阶段：电影详情获取

#### 2.1 开始获取详情
**位置：** `VideoActivity.getDetail()`
**关键操作：**
- 设置ViewModel的FlowID
- 调用详情获取接口

**日志输出：**
```
=== [FlowID:MOVIE_12345] === 开始获取电影详情 ===
[FlowID:MOVIE_12345] [DETAIL_START] VideoActivity.getDetail called, key: site1, id: movie123
```

#### 2.2 SiteViewModel处理详情请求
**位置：** `SiteViewModel.detailContent()`
**关键操作：**
- 获取站点信息
- 根据站点类型选择处理方式：
  - **类型3：** JavaScript Spider
  - **类型4：** HTTP API Extension  
  - **Push Agent：** 推送代理
  - **标准HTTP API**

**日志输出：**
```
[FlowID:MOVIE_12345] [DETAIL_CONTENT_START] 开始获取详情: key=site1, id=movie123
[FlowID:MOVIE_12345] [DETAIL_CONTENT_SITE] 站点信息: 站点名称 (类型:3, API:http://...)
[FlowID:MOVIE_12345] [DETAIL_CONTENT_COMPLETE] 详情获取完成: 影片=电影名称, 线路数=5, 耗时=1200ms
```

#### 2.3 显示详情页面
**位置：** `VideoActivity.setDetail()`
**关键操作：**
- 解析电影详情数据
- 显示播放线路和集数列表
- 准备播放界面

### 第三阶段：播放地址获取

#### 3.1 用户选择播放
**触发：** 用户选择具体的播放线路和集数
**位置：** `VideoActivity.getPlayer()`

**日志输出：**
```
[FlowID:MOVIE_12345] [EPISODE_SELECT] 选择集数: 第1集
```

#### 3.2 SiteViewModel获取播放地址
**位置：** `SiteViewModel.playerContent()`
**关键操作：**
- 根据站点类型获取播放地址
- **JavaScript Spider (类型3)：**
  ```java
  Spider spider = site.spider(flowId);
  String result = spider.playerContent(flag, id, vipFlags);
  ```
- **HTTP API Extension (类型4)：**
  ```java
  String response = OkHttp.newCall(url, headers).execute().body().string();
  ```
- **Push Agent：**
  ```java
  Result result = Source.get().fetch(result);
  ```

**日志输出：**
```
[FlowID:MOVIE_12345] [PLAYER_CONTENT_START] 获取播放地址: key=site1, flag=线路1, id=ep001
[FlowID:MOVIE_12345] [PLAYER_CONTENT_SITE] 站点信息: 站点名称 (类型:3)
[FlowID:MOVIE_12345] [PLAYER_CONTENT_JS] JavaScript Spider获取播放地址
[FlowID:MOVIE_12345] [PLAYER_CONTENT_COMPLETE] 播放地址获取完成: URL=http://..., 需要解析=true, 耗时=800ms
```

### 第四阶段：播放器启动和URL解析

#### 4.1 播放器启动
**位置：** `VideoActivity.setPlayer() → Players.start()`
**关键操作：**
- 设置播放器FlowID
- 判断是否需要URL解析
- 启动播放器或解析器

**日志输出：**
```
=== [FlowID:MOVIE_12345] === 播放器开始播放 ===
[FlowID:MOVIE_12345] [PLAYER_START] 播放器开始: URL=http://..., 需要解析=true, 超时=30000ms
[FlowID:MOVIE_12345] [PLAYER_PARSE] 需要解析，启动解析器
```

#### 4.2 URL解析处理
**位置：** `ParseJob.start() → doInBackground()`
**解析类型：**

**嗅探解析 (Type 0)：**
```java
startWeb(key, parse, webUrl);
```

**JSON解析 (Type 1)：**
```java
jsonParse(parse, webUrl, true);
```

**JSON扩展解析 (Type 2)：**
```java
jsonExtend(webUrl);
```

**JSON聚合解析 (Type 3)：**
```java
jsonMix(webUrl, flag);
```

**超级解析 (Type 4)：**
```java
godParse(webUrl, flag);
```

**日志输出：**
```
[FlowID:MOVIE_12345] [PARSE_START] 开始解析URL: http://..., useParse: true
[FlowID:MOVIE_12345] [PARSE_CONFIG] 解析器配置: 类型=1, 名称=JSON解析器, URL=http://...
[FlowID:MOVIE_12345] [PARSE_JSON] 开始JSON解析
[FlowID:MOVIE_12345] [PARSE_SUCCESS] 解析成功: URL=http://real-video-url.m3u8, From=JSON解析器
```

#### 4.3 最终播放
**位置：** `Players.setMediaItem()`
**关键操作：**
- 使用解析后的真实URL
- 设置ExoPlayer媒体项
- 开始视频播放

**日志输出：**
```
[FlowID:MOVIE_12345] [PLAYER_DIRECT] 直接播放URL: http://real-video-url.m3u8
```

## URL解析详细过程

### 解析器类型说明

| 类型 | 名称 | 描述 | 使用场景 |
|------|------|------|----------|
| 0 | 嗅探解析 | 通过WebView嗅探真实播放地址 | 需要JavaScript执行的复杂页面 |
| 1 | JSON解析 | 通过JSON API获取播放地址 | 标准的JSON接口 |
| 2 | JSON扩展解析 | 扩展的JSON解析方式 | 需要特殊处理的JSON接口 |
| 3 | JSON聚合解析 | 聚合多个JSON源 | 多源聚合场景 |
| 4 | 超级解析 | 高级解析算法 | 复杂的解析需求 |

### 解析流程示例

#### JSON解析流程 (Type 1)
```java
// 1. 构建请求URL
String requestUrl = parse.getUrl() + webUrl;

// 2. 发送HTTP请求
String response = OkHttp.newCall(requestUrl, headers).execute().body().string();

// 3. 解析JSON响应
JsonObject jsonObject = Json.parse(response).getAsJsonObject();

// 4. 提取播放URL
String playUrl = jsonObject.get("url").getAsString();

// 5. 验证和返回结果
if (playUrl.length() > 40) {
    onParseSuccess(headers, playUrl, "JSON解析器");
} else {
    onParseError();
}
```

## 错误处理机制

### 常见错误类型

1. **DRM不支持**
   ```
   [FlowID:MOVIE_12345] [PLAYER_ERROR] DRM不支持
   ```

2. **URL解析失败**
   ```
   [FlowID:MOVIE_12345] [PARSE_ERROR] 解析失败
   ```

3. **网络请求失败**
   ```
   [FlowID:MOVIE_12345] [PLAYER_ERROR] 提取错误: 网络连接失败
   ```

4. **非法URL**
   ```
   [FlowID:MOVIE_12345] [PLAYER_ERROR] 非法URL: invalid_url
   ```

## 性能监控

### 关键性能指标

1. **详情获取耗时**
   - 正常范围：500-2000ms
   - 超时阈值：5000ms

2. **播放地址获取耗时**
   - 正常范围：300-1500ms
   - 超时阈值：3000ms

3. **URL解析耗时**
   - 正常范围：100-800ms
   - 超时阈值：30000ms

4. **整体播放启动耗时**
   - 正常范围：1000-4000ms
   - 用户感知阈值：5000ms

## 总结

通过这个完整的流程解析，我们可以看到TV-Vod项目的播放系统是一个复杂的多层架构：

1. **用户交互层**：处理用户点击和选择
2. **业务逻辑层**：管理电影详情和播放地址获取
3. **数据获取层**：与各种站点API交互
4. **URL解析层**：处理各种复杂的URL解析需求
5. **播放器层**：最终的视频播放

每个层次都有详细的日志跟踪，确保整个播放流程的可观测性和可调试性。通过FlowID机制，可以完整跟踪单次播放请求的全生命周期，大大提高了问题定位和性能优化的效率。
