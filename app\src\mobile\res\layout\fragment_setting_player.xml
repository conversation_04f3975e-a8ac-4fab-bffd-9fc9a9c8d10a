<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/transparent"
        android:elevation="0dp"
        app:elevation="0dp"
        app:liftOnScrollColor="@color/transparent">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:text="@string/setting_player"
            android:textColor="@color/white"
            android:textSize="20sp"
            android:textStyle="bold"
            app:layout_scrollFlags="scroll|enterAlways" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:overScrollMode="never"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:animateLayoutChanges="true"
            android:orientation="vertical"
            android:padding="16dp">

            <LinearLayout
                android:id="@+id/render"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/shape_item"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:text="@string/player_render"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/renderText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    tools:text="Surface" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/scale"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/shape_item"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:text="@string/player_scale"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/scaleText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    tools:text="預設" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/caption"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/shape_item"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:text="@string/player_caption"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/captionText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="middle"
                    android:gravity="end"
                    android:singleLine="true"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    tools:text="預設" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/buffer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/shape_item"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:text="@string/player_buffer"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/bufferText"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    tools:text="1" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:text="@string/times"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/speed"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/shape_item"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:text="@string/player_speed"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/speedText"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    tools:text="1" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:text="@string/times"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/tunnel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/shape_item"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:text="@string/player_tunnel"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/tunnelText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    tools:text="關" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/audioDecode"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/shape_item"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:text="@string/player_audio_decode"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/audioDecodeText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    tools:text="開" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/aac"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/shape_item"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:text="@string/player_aac_track"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/aacText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    tools:text="開" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/danmakuLoad"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/shape_item"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:text="@string/player_danmaku_load"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/danmakuLoadText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    tools:text="開" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/background"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/shape_item"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:text="@string/player_background"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/backgroundText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    tools:text="畫中畫" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/ua"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/shape_item"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:text="@string/player_ua"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/uaText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="middle"
                    android:gravity="end"
                    android:singleLine="true"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    tools:text="okhttp/4.11.0" />

            </LinearLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.coordinatorlayout.widget.CoordinatorLayout>