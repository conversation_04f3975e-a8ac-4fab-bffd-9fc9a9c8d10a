<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_vod_list"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:foreground="@drawable/selector_vod">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/image"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_margin="16dp"
            android:scaleType="fitCenter"
            tools:src="@drawable/ic_folder" />

        <TextView
            android:id="@+id/name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:singleLine="true"
            android:textColor="@color/white"
            android:textSize="16sp"
            tools:text="Download" />

    </LinearLayout>
</FrameLayout>