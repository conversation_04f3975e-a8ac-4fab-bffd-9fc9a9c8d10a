<?xml version="1.0" encoding="utf-8"?>
<com.fongmi.android.tv.ui.custom.CustomLeftRightLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="56dp"
    android:background="@drawable/selector_channel"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:gravity="center"
    android:orientation="horizontal"
    android:paddingStart="20dp"
    android:paddingEnd="20dp">

    <TextView
        android:id="@+id/number"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="12dp"
        android:duplicateParentState="true"
        android:textColor="@color/channel"
        android:textSize="16sp"
        android:textStyle="bold"
        tools:text="01" />

    <ImageView
        android:id="@+id/logo"
        android:layout_width="48dp"
        android:layout_height="36dp"
        android:layout_marginEnd="12dp"
        android:scaleType="fitCenter"
        android:visibility="gone" />

    <TextView
        android:id="@+id/name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:duplicateParentState="true"
        android:ellipsize="marquee"
        android:singleLine="true"
        android:textColor="@color/channel"
        android:textSize="16sp"
        android:textStyle="bold"
        tools:text="CNN" />

</com.fongmi.android.tv.ui.custom.CustomLeftRightLayout>