<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:keepScreenOn="true"
    android:orientation="vertical">

    <FrameLayout
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="24dp"
        android:paddingTop="24dp"
        android:paddingEnd="24dp"
        android:paddingBottom="8dp">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:animateLayoutChanges="true"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/logo"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_marginEnd="12dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:visibility="gone"
                tools:src="@drawable/ic_logo"
                tools:visibility="visible" />

            <com.fongmi.android.tv.ui.custom.CustomTitleView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center|start"
                android:focusable="false"
                android:text="@string/app_name"
                android:textColor="@color/white"
                android:textSize="24sp" />

        </LinearLayout>

        <TextView
            android:id="@+id/clock"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:textColor="@color/white"
            android:textSize="24sp"
            tools:text="07/25 09:20:00" />

    </FrameLayout>

    <com.fongmi.android.tv.ui.custom.ProgressLayout
        android:id="@+id/progressLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.leanback.widget.VerticalGridView
            android:id="@+id/recycler"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:paddingStart="24dp"
            android:paddingTop="8dp"
            android:paddingEnd="24dp"
            android:paddingBottom="24dp"
            app:focusOutEnd="true"
            app:focusOutFront="true" />

    </com.fongmi.android.tv.ui.custom.ProgressLayout>
</LinearLayout>