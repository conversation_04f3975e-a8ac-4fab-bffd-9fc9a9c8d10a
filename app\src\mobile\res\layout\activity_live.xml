<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:keepScreenOn="true">

    <FrameLayout
        android:id="@+id/video"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black"
        android:clickable="true"
        android:focusable="true">

        <androidx.media3.ui.PlayerView
            android:id="@+id/exo"
            style="@style/Player.Live"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:surface_type="surface_view" />

    </FrameLayout>

    <include
        android:id="@+id/widget"
        layout="@layout/view_widget_live"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <include
        android:id="@+id/control"
        layout="@layout/view_control_live"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone" />

    <LinearLayout
        android:id="@+id/recycler"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:background="@drawable/shape_live_list"
        android:orientation="horizontal"
        android:visibility="gone"
        tools:visibility="visible">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/group"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:padding="8dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:itemCount="5"
            tools:listitem="@layout/adapter_group" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/channel"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:padding="8dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:itemCount="5"
            tools:listitem="@layout/adapter_channel" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/epgData"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:padding="8dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:itemCount="5"
            tools:listitem="@layout/adapter_epg_data" />

    </LinearLayout>
</FrameLayout>

