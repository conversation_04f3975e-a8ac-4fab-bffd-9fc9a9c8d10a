<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/position"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/grey_700"
        android:textSize="16sp"
        tools:text="00:00:00" />

    <androidx.media3.ui.DefaultTimeBar
        android:id="@+id/timeBar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:layout_weight="1"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:nextFocusLeft="@id/timeBar"
        android:nextFocusRight="@id/timeBar"
        android:nextFocusUp="@id/next"
        android:nextFocusDown="@id/timeBar"
        app:buffered_color="@color/blue_200"
        app:played_color="@color/blue_500"
        app:scrubber_color="@color/blue_500"
        app:unplayed_color="@color/grey_500" />

    <TextView
        android:id="@+id/duration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/grey_700"
        android:textSize="16sp"
        tools:text="00:00:00" />

</LinearLayout>