<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginTop="6dp"
    android:layout_marginBottom="6dp"
    android:background="@drawable/shape_channel"
    android:gravity="center"
    android:orientation="horizontal"
    android:paddingStart="12dp"
    android:paddingTop="6dp"
    android:paddingEnd="12dp"
    android:paddingBottom="6dp">

    <TextView
        android:id="@+id/number"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="12dp"
        android:duplicateParentState="true"
        android:textColor="@color/channel"
        android:textSize="14sp"
        tools:text="01" />

    <ImageView
        android:id="@+id/logo"
        android:layout_width="44dp"
        android:layout_height="33dp"
        android:layout_marginEnd="12dp"
        android:scaleType="fitCenter"
        android:visibility="gone" />

    <TextView
        android:id="@+id/name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:duplicateParentState="true"
        android:ellipsize="marquee"
        android:singleLine="true"
        android:textColor="@color/channel"
        android:textSize="14sp"
        tools:text="CNN" />

</LinearLayout>