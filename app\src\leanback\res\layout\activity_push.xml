<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black_20"
    android:gravity="center"
    android:keepScreenOn="true"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/code"
        android:layout_width="250dp"
        android:layout_height="250dp"
        android:scaleType="fitXY" />

    <TextView
        android:id="@+id/info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:gravity="center"
        android:lineSpacingExtra="8dp"
        android:shadowColor="@color/grey_900"
        android:shadowDx="2"
        android:shadowDy="2"
        android:shadowRadius="1"
        android:textColor="@color/white"
        android:textSize="18sp" />

    <TextView
        android:id="@+id/clip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:background="@drawable/shape_item_normal"
        android:text="@string/push_clip"
        android:textColor="@color/white"
        android:textSize="18sp" />

</LinearLayout>