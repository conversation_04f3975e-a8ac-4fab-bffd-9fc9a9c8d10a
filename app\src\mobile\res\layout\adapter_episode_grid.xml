<?xml version="1.0" encoding="utf-8"?>
<TextView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/text"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="6dp"
    android:background="@drawable/shape_accent"
    android:ellipsize="marquee"
    android:focusable="true"
    android:gravity="center"
    android:padding="8dp"
    android:singleLine="true"
    android:textColor="@color/control"
    android:textSize="14sp"
    tools:text="20" />