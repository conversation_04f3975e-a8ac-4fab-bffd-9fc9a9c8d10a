<resources>

    <style name="AppTheme" parent="BaseTheme" />

    <style name="AppTheme.Live" parent="AppTheme">
        <item name="android:windowBackground">@color/black</item>
    </style>

    <style name="BaseTheme" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryDark">@color/primaryDark</item>
        <item name="colorAccent">@color/accent</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowBackground">@null</item>
        <item name="android:windowDisablePreview">true</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <item name="bottomSheetDialogTheme">@style/BottomSheetDialog</item>
    </style>

    <style name="ModalBottomSheetDialog" parent="Widget.Design.BottomSheet.Modal">
        <item name="android:background">@drawable/shape_bottom_sheet</item>
    </style>

    <style name="BottomSheetDialog" parent="Theme.MaterialComponents.Light.BottomSheetDialog">
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryDark">@color/primaryDark</item>
        <item name="colorAccent">@color/accent</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowIsFloating">false</item>
        <item name="bottomSheetStyle">@style/ModalBottomSheetDialog</item>
    </style>

</resources>
