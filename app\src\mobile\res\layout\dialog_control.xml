<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    tools:background="@color/white">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/control_speed"
        android:textColor="?android:attr/textColorPrimary"
        android:textSize="16sp" />

    <com.google.android.material.slider.Slider
        android:id="@+id/speed"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="-12dp"
        android:layout_marginEnd="-12dp"
        android:stepSize="0.25"
        android:valueFrom="0.5"
        android:valueTo="5"
        app:tickVisible="false"
        app:trackColorInactive="@color/blue_50" />

    <TextView
        android:id="@+id/parseText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:text="@string/parse"
        android:textColor="?android:attr/textColorPrimary"
        android:textSize="16sp"
        android:visibility="gone"
        tools:visibility="visible" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/parse"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:orientation="horizontal"
        android:visibility="gone"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        tools:itemCount="5"
        tools:listitem="@layout/adapter_parse_light"
        tools:visibility="visible" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:text="@string/control_scale"
        android:textColor="?android:attr/textColorPrimary"
        android:textSize="16sp" />

    <HorizontalScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fillViewport="true"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/scale_0"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:background="@drawable/shape_accent"
                android:tag="0"
                android:textColor="@color/control"
                android:textSize="14sp"
                tools:text="預設" />

            <TextView
                android:id="@+id/scale_1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:background="@drawable/shape_accent"
                android:tag="1"
                android:textColor="@color/control"
                android:textSize="14sp"
                tools:text="16:9" />

            <TextView
                android:id="@+id/scale_2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:background="@drawable/shape_accent"
                android:tag="2"
                android:textColor="@color/control"
                android:textSize="14sp"
                tools:text="4:3" />

            <TextView
                android:id="@+id/scale_3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:background="@drawable/shape_accent"
                android:tag="3"
                android:textColor="@color/control"
                android:textSize="14sp"
                tools:text="填充" />

            <TextView
                android:id="@+id/scale_4"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/shape_accent"
                android:tag="4"
                android:textColor="@color/control"
                android:textSize="14sp"
                tools:text="裁剪" />

        </LinearLayout>
    </HorizontalScrollView>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="8dp"
        android:text="@string/play"
        android:textColor="?android:attr/textColorPrimary"
        android:textSize="16sp" />

    <HorizontalScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fillViewport="true"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/player"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:background="@drawable/shape_accent"
                android:textColor="@color/control"
                android:textSize="14sp"
                tools:text="EXO" />

            <TextView
                android:id="@+id/decode"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:background="@drawable/shape_accent"
                android:textColor="@color/control"
                android:textSize="14sp"
                tools:text="硬解" />

            <TextView
                android:id="@+id/opening"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:background="@drawable/shape_accent"
                android:textColor="@color/control"
                android:textSize="14sp"
                tools:text="片頭" />

            <TextView
                android:id="@+id/ending"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:background="@drawable/shape_accent"
                android:textColor="@color/control"
                android:textSize="14sp"
                tools:text="片尾" />

        </LinearLayout>
    </HorizontalScrollView>

    <TextView
        android:id="@+id/track"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="8dp"
        android:text="@string/control_track"
        android:textColor="?android:attr/textColorPrimary"
        android:textSize="16sp"
        android:visibility="gone"
        tools:visibility="visible" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:background="@drawable/shape_accent"
            android:tag="3"
            android:text="@string/play_track_text"
            android:textColor="@color/control"
            android:textSize="14sp"
            android:visibility="gone"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/audio"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:background="@drawable/shape_accent"
            android:tag="1"
            android:text="@string/play_track_audio"
            android:textColor="@color/control"
            android:textSize="14sp"
            android:visibility="gone"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/video"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_accent"
            android:tag="2"
            android:text="@string/play_track_video"
            android:textColor="@color/control"
            android:textSize="14sp"
            android:visibility="gone"
            tools:visibility="visible" />

    </LinearLayout>

    <TextView
        android:id="@+id/other"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="8dp"
        android:text="@string/control_other"
        android:textColor="?android:attr/textColorPrimary"
        android:textSize="16sp" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/danmaku"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:background="@drawable/shape_accent"
            android:text="@string/danmaku"
            android:textColor="@color/control"
            android:textSize="14sp"
            android:visibility="gone"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/loop"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:background="@drawable/shape_accent"
            android:text="@string/play_loop"
            android:textColor="@color/control"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/timer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_accent"
            android:text="@string/play_timer"
            android:textColor="@color/control"
            android:textSize="14sp" />

    </LinearLayout>
</LinearLayout>